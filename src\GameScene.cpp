#include "../include/GameScene.hpp"
#include <SFML/Graphics.hpp>
#include "../include/GameConfig.hpp"


GameScene::GameScene(int width, int height, const char *title)
{
    windowWidth = width;
    windowHeight = height;

    // 初始化游戏配置
    GameConfig::initialize(width, height);

    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);

    // 创建物理世界
    physicsWorld = new PhysicsWorld(windowWidth, windowHeight);

    // 初始化玩家
    player1 = new Player(sf::Color::Green, physicsWorld->getWorldId());
    player1->setPosition(GameConfig::getPlayer1StartPos().x, GameConfig::getPlayer1StartPos().y);
    player2 = new Player(sf::Color::Blue, physicsWorld->getWorldId());
    player2->setPosition(GameConfig::getPlayer2StartPos().x, GameConfig::getPlayer2StartPos().y);

    // 初始化羽毛球
    shuttlecock = new Shuttlecock(physicsWorld->getWorldId(), sf::Vector2f(GameConfig::getNetX(), GameConfig::getNetY()));

    clock.restart();
    // 进入游戏循环
    while (window->isOpen())
    {

        sf::Event event;
        if(window->pollEvent(event))
        {
            if(event.type == sf::Event::Closed)
            {
                window->close();
            }
            else if (event.type == sf::Event::KeyPressed||event.type == sf::Event::KeyReleased)
            {
                // 处理玩家输入
                processGamingInput(event);
            }
        }
        controlPlayer();
        renderGame();
    }
}

void GameScene::processGamingInput(sf::Event &event)
{
    if(event.type == sf::Event::KeyPressed)
    {
        pressedKeys.insert(event.key.code);
    }
    if(event.type == sf::Event::KeyReleased)
    {
        pressedKeys.erase(event.key.code);
    }
}

void GameScene::controlPlayer(){
    if(pressedKeys.find(sf::Keyboard::A)!= pressedKeys.end())
    {
        player1->move(Direction(sf::Vector2f(-1.0, 0)), 1.0f / 60.0f);
    }
    else if(pressedKeys.find(sf::Keyboard::D)!= pressedKeys.end())
    {
        player1->move(Direction(sf::Vector2f(1.0, 0)), 1.0f / 60.0f);
    }
    else player1->move(Direction(sf::Vector2f(0, 0)), 1.0f / 60.0f);

}

void GameScene::renderGame()
{
    float deltaTime = clock.restart().asSeconds();
    // 背景设置成白色
    window->clear(sf::Color::White);
    physicsWorld->step(deltaTime);
    physicsWorld->render(*window);
    player1->draw(*window);
    player2->draw(*window);
    shuttlecock->render(*window);
    window->display();
}

GameScene::~GameScene()
{
    delete player1;
    delete player2;
    delete shuttlecock;
    delete physicsWorld;
    delete window;
}