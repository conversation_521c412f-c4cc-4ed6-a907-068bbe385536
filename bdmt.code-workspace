{"folders": [{"path": "."}], "settings": {"cmake.cmakePath": "E:\\Tools\\MSYS2\\mingw64\\bin\\cmake.exe", "cmake.ctestPath": "E:\\Tools\\MSYS2\\mingw64\\bin\\ctest.exe", "cmake.useVsDeveloperEnvironment": "auto", "cmake.generator": "Ninja", "files.associations": {"cmath": "cpp", "chrono": "cpp", "bit": "cpp", "bitset": "cpp", "charconv": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "optional": "cpp", "format": "cpp", "fstream": "cpp", "iomanip": "cpp", "ios": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "ranges": "cpp", "sstream": "cpp", "streambuf": "cpp", "system_error": "cpp", "type_traits": "cpp", "tuple": "cpp", "variant": "cpp", "new": "cpp"}, "[cpp]": {"editor.wordBasedSuggestions": "off", "editor.semanticHighlighting.enabled": true, "editor.stickyScroll.defaultModel": "foldingProviderModel", "editor.suggest.insertMode": "replace", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}}}