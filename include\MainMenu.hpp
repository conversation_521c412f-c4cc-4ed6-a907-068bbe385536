#pragma once
#include <SFML/Graphics.hpp>

class MainMenu
{
public:
    MainMenu(int windowWidth, int windowHeight);
    ~MainMenu();
    
    void handleInput(sf::Event& event);
    void render(sf::RenderWindow& window);
    bool shouldStartGame() const;
    void reset(); // 重置状态，用于从游戏返回主界面时
    
private:
    sf::Font font;
    sf::Text titleText;
    sf::Text startText;
    sf::Text instructionText;
    
    bool startGameRequested;
    int windowWidth;
    int windowHeight;
    
    void setupTexts();
};
