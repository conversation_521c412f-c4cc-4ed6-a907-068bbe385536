#include "../include/Racket.hpp"
#include <cmath>

Racket::Racket(b2WorldId worldId, sf::Vector2f playerPosition, sf::Color color)
    : worldId(worldId), playerPosition(playerPosition), angle(0.0f), racketColor(color)
{
    createPhysicsBody();
    setRenderShapes();
    angularVelocity = GameConfig::RACKET_ROTATION_SPEED;
}

Racket::~Racket()
{
    if (B2_IS_NON_NULL(handleBody) && B2_IS_NON_NULL(worldId))
    {
        b2DestroyBody(handleBody);
    }
    if (B2_IS_NON_NULL(headBody) && B2_IS_NON_NULL(worldId))
    {
        b2DestroyBody(headBody);
    }
}
void Racket::setRenderShapes()
{
    // 设置渲染形状 - 修复连接问题
    handleShape.setSize(sf::Vector2f(GameConfig::getHandleWidth(), GameConfig::getHandleLength()));
    handleShape.setOrigin(GameConfig::getHandleWidth() / 2.0f, GameConfig::getHandleLength()); // 从底部开始
    handleShape.setFillColor(sf::Color(139, 69, 19));                                          // 棕色拍柄

    headShape.setSize(sf::Vector2f(GameConfig::getHeadWidth(), GameConfig::getHeadHeight()));
    headShape.setOrigin(GameConfig::getHeadWidth() / 2.0f, GameConfig::getHeadHeight()); // 从底部开始
    headShape.setFillColor(racketColor);
}

void Racket::createPhysicsBody()
{
    b2BodyDef handleDef = b2DefaultBodyDef();
    handleDef.type = b2_kinematicBody;
    handleDef.position = PhysicsWorld::sfToB2(playerPosition);
    handleDef.rotation = b2MakeRot(angle);
    handleBody = b2CreateBody(worldId, &handleDef);

    b2BodyDef headDef = b2DefaultBodyDef();
    headDef.type = b2_dynamicBody;
    headDef.position = PhysicsWorld::sfToB2(playerPosition);
    headDef.rotation = b2MakeRot(angle);
    headBody = b2CreateBody(worldId, &headDef);

    // 创建矩形碰撞体
    b2Polygon headBox = b2MakeBox(
        (GameConfig::getHeadWidth() / 2.0f) / PhysicsWorld::PIXELS_PER_METER,
        (GameConfig::getHeadHeight() / 2.0f) / PhysicsWorld::PIXELS_PER_METER);
    b2ShapeDef headShapeDef = b2DefaultShapeDef();
    headShapeDef.density = 2.0f;

    b2ShapeId headShapeId = b2CreatePolygonShape(headBody, &headShapeDef, &headBox);

    b2Shape_SetRestitution(headShapeId, 1.0f);

    b2Polygon handleBox = b2MakeBox(
        (GameConfig::getHandleWidth() / 2.0f) / PhysicsWorld::PIXELS_PER_METER,
        (GameConfig::getHandleLength() / 2.0f) / PhysicsWorld::PIXELS_PER_METER);
    b2ShapeDef handleShapeDef = b2DefaultShapeDef();
    handleShapeDef.isSensor = true;
    b2ShapeId handleShapeId = b2CreatePolygonShape(handleBody, &handleShapeDef, &handleBox);

    // 创建固定关节连接
    b2WeldJointDef jointDef = b2DefaultWeldJointDef();

    // 计算关节位置
    // 在球杆顶部中心
    b2Vec2 localAnchorA = {0.0f, GameConfig::getHandleLength() / 2.0f / PhysicsWorld::PIXELS_PER_METER};
    b2Vec2 localAnchorB = {0.0f, -GameConfig::getHeadHeight() / 2.0f / PhysicsWorld::PIXELS_PER_METER};
    // 设置关节连接球拍和球杆
    jointDef.base.bodyIdA = handleBody;
    jointDef.base.bodyIdB = headBody;
    jointDef.base.localFrameA.p = localAnchorA;
    jointDef.base.localFrameB.p = localAnchorB;

    b2JointId jointId = b2CreateWeldJoint(worldId, &jointDef);
}

void Racket::updateState()
{
    // 从球拍的物理位置更新渲染位置
    if (B2_IS_NON_NULL(handleBody))
    {
        b2Vec2 physicsPos = b2Body_GetPosition(handleBody);
        sf::Vector2f newPos = PhysicsWorld::b2ToSf(physicsPos);
        playerPosition = newPos;
        angle = b2Rot_GetAngle(b2Body_GetRotation(handleBody));
        handleShape.setPosition(playerPosition);
        handleShape.setRotation(angle);
        headShape.setPosition(playerPosition);
        headShape.setRotation(angle);
    }
}

void Racket::draw(sf::RenderWindow &window)
{
    updateState();
    window.draw(handleShape);
    window.draw(headShape);
}

void Racket::createJoint(b2WorldId worldId,b2BodyId playerBody,b2Vec2 playerAnchor)
{
    b2RevoluteJointDef jointDef = b2DefaultRevoluteJointDef();
    jointDef.base.bodyIdA = playerBody;
    jointDef.base.bodyIdB = handleBody;
    jointDef.base.localFrameA.p = playerAnchor;
    jointDef.base.localFrameB.p = {0.0f, GameConfig::getHeadHeight() / 2.0f / PhysicsWorld::PIXELS_PER_METER};
    jointId = b2CreateRevoluteJoint(worldId, &jointDef);
}

void Racket::setJointAngle(float TargetAngle,float motorSpeed,float maxTorque){
    float currentAngle = b2RevoluteJoint_GetTargetAngle(jointId);
    float diff = TargetAngle - currentAngle;
    
    //归一化
    while (diff > B2_PI) diff -= 2.0 * B2_PI;
    while (diff < -B2_PI) diff += 2.0 * B2_PI;

    b2RevoluteJoint_EnableMotor(jointId, true);
    b2RevoluteJoint_SetMotorSpeed(jointId, motorSpeed);
    b2RevoluteJoint_SetMaxMotorTorque(jointId, maxTorque);
    b2RevoluteJoint_SetTargetAngle(jointId, TargetAngle);
}
