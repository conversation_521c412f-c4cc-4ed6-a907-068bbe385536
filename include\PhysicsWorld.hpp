#pragma once
#include <box2d/box2d.h>
#include <SFML/Graphics.hpp>
#include "GameConfig.hpp"

class PhysicsWorld
{
public:
    PhysicsWorld(int windowWidth, int windowHeight);
    ~PhysicsWorld();

    void step(float deltaTime);
    void render(sf::RenderWindow &window);

    b2WorldId getWorldId() { return worldId; }

    // 碰撞检测相关
    void handleCollisions();

    // 像素到物理世界的转换比例（使用GameConfig中的值）
    static const float PIXELS_PER_METER;

    // 坐标转换函数
    static sf::Vector2f b2ToSf(const b2Vec2 &vec);
    static b2Vec2 sfToB2(const sf::Vector2f &vec);

private:
    b2WorldId worldId;

    // 场地元素的物理体
    b2BodyId ground;
    b2BodyId leftWall;
    b2BodyId rightWall;
    b2BodyId net;

    // 用于渲染的SFML图形
    sf::RectangleShape groundShape;
    sf::RectangleShape leftWallShape;
    sf::RectangleShape rightWallShape;
    sf::RectangleShape netShape;

    int windowWidth;
    int windowHeight;

    void createGround();
    void createWalls();
    void createNet();
    void setupRenderShapes();
};
