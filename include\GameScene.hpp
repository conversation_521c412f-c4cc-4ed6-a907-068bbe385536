#include <sfml/Graphics.hpp>
#include "Player.hpp"
#include "PhysicsWorld.hpp"
#include "GameConfig.hpp"
#include "Shuttlecock.hpp"
#include <set>


class GameScene
{
public:
    GameScene(int width = 800, int height = 600, const char *title = "Badminton Game");
    void processGamingInput(sf::Event &event);
    void renderGame();
    void controlPlayer();
    ~GameScene();

private:
    sf::RenderWindow *window;
    std::set<sf::Keyboard::Key> pressedKeys;
    sf::Clock clock;


    // 物理世界
    PhysicsWorld *physicsWorld;

    // 玩家对象
    Player *player1;
    Player *player2;

    // 羽毛球
    Shuttlecock *shuttlecock;

    // 窗口尺寸
    int windowWidth;
    int windowHeight;
};