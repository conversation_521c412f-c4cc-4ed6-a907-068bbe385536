#include "../include/GameConfig.hpp"

// 静态成员变量定义
int GameConfig::WINDOW_WIDTH = 800;
int GameConfig::WINDOW_HEIGHT = 600;

// 按真实羽毛球场比例：13.4m x 6.1m，使用更合适的像素比例
const float GameConfig::PIXELS_PER_METER = GameConfig::WINDOW_WIDTH / 13.4f;
const float GameConfig::GRAVITY = 9.8f;

// 调整速度以匹配新的场地比例
const float GameConfig::PLAYER_SPEED = 50.0f;

// 增加球拍旋转速度
const float GameConfig::RACKET_ROTATION_SPEED = 4.0f; // 弧度/秒
const sf::Color GameConfig::racketColor = sf::Color::Yellow;

const float GameConfig::BALL_DENSITY = 0.05f;    // 羽毛球很轻
const float GameConfig::BALL_RESTITUTION = 1.0f; // 完全弹性碰撞

// 增加跳跃力度，确保能跳过网
const float GameConfig::JUMP_FORCE = 400.0f;

void GameConfig::initialize(int windowWidth, int windowHeight)
{
    WINDOW_WIDTH = windowWidth;
    WINDOW_HEIGHT = windowHeight;
}
