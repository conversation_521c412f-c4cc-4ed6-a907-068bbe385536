# CMakeLists.txt
cmake_minimum_required(VERSION 3.14) # FetchContent 需要较新版本
project(bdmt)
# 设置 SFML 为静态链接（这是 SFML 特有的选项）
set(SFML_STATIC ON CACHE BOOL "Statically link SFML libraries")

# 设置全局默认使用静态库（影响所有通过 FetchContent 获取的库）
set(BUILD_SHARED_LIBS OFF CACHE BOOL "Build static libraries instead of shared libraries")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)

# 1. 下载并编译 SFML
include(FetchContent)
FetchContent_Declare(
  sfml
  URL https://github.com/SFML/SFML/archive/2.6.1.tar.gz # 或使用具体的版本URL
)
FetchContent_MakeAvailable(sfml)

# 2. 下载并编译 Box2D
FetchContent_Declare(
  box2d
  GIT_REPOSITORY https://github.com/erincatto/box2d.git
  GIT_TAG        main # 或指定一个稳定版本标签，如 v2.4.1
)
FetchContent_MakeAvailable(box2d)

# 添加你的可执行文件
add_executable(${PROJECT_NAME}
    src/main.cpp
    src/HomePage.cpp
    src/GameScene.cpp
    src/Player.cpp
    src/MainMenu.cpp
    src/PhysicsWorld.cpp
    src/Racket.cpp
    src/GameConfig.cpp
    src/Shuttlecock.cpp
)

# 链接库
target_link_libraries(${PROJECT_NAME} PRIVATE
    sfml-graphics sfml-window sfml-system
    box2d
)
