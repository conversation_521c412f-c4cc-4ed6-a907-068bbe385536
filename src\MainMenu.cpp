#include "../include/MainMenu.hpp"

MainMenu::MainMenu(int windowWidth, int windowHeight)
    : windowWidth(windowWidth), windowHeight(windowHeight), startGameRequested(false)
{
    // 加载字体（使用系统默认字体）
    if (!font.loadFromFile("C:/Windows/Fonts/arial.ttf"))
    {
        
    }

    setupTexts();
}

MainMenu::~MainMenu()
{
}

void MainMenu::setupTexts()
{
    // 设置标题文本
    titleText.setFont(font);
    titleText.setString("Badminton Game");
    titleText.setCharacterSize(48);
    titleText.setFillColor(sf::Color::Black);
    titleText.setPosition(windowWidth / 3, windowHeight / 3);

    // 设置开始游戏文本
    startText.setFont(font);
    startText.setString("Press SPACE to Start Game");
    startText.setCharacterSize(24);
    startText.setFillColor(sf::Color::Blue);
    startText.setPosition(windowWidth / 2 , windowHeight / 2);

    // 设置说明文本
    instructionText.setFont(font);
    instructionText.setString("Player 1: A/D-Move, W/S-Racket, Q-Jump\nPlayer 2: Arrow Keys-Move, Up/Down-Racket, RShift-Jump");
    instructionText.setCharacterSize(16);
    instructionText.setFillColor(sf::Color(128, 128, 128)); // 灰色
    instructionText.setPosition(windowWidth / 5, windowHeight / 2 + 50);
}

void MainMenu::handleInput(sf::Event &event)
{
    if (event.type == sf::Event::KeyPressed)
    {
        if (event.key.code == sf::Keyboard::Space)
        {
            startGameRequested = true;
        }
    }
}

void MainMenu::render(sf::RenderWindow &window)
{
    window.draw(titleText);
    window.draw(startText);
    window.draw(instructionText);
}

bool MainMenu::shouldStartGame() const
{
    return startGameRequested;
}

void MainMenu::reset()
{
    startGameRequested = false;
}
