#pragma once
#include <SFML/Graphics.hpp>
#include <box2d/box2d.h>
#include "PhysicsWorld.hpp"
#include "GameConfig.hpp"

class Shuttlecock
{
public:
    Shuttlecock(b2WorldId worldId, sf::Vector2f startPosition);
    ~Shuttlecock();

    void update(float deltaTime);
    void render(sf::RenderWindow &window);
    void reset(sf::Vector2f newPosition);

    sf::Vector2f getPosition() const;
    bool isOutOfBounds(int windowWidth, int windowHeight) const;
    bool hasHitGround(int windowHeight) const;
    sf::Vector2f getResetPosition(int windowWidth, int windowHeight) const;

private:
    b2WorldId worldId;
    b2BodyId physicsBody;

    // 渲染用的SFML图形
    sf::CircleShape ballShape;

    sf::Vector2f position;

    void createPhysicsBody();
    void updateFromPhysics();
    void applyAirResistance();
};
