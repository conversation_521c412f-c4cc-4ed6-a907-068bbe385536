#include "../include/Player.hpp"
#include "../include/Racket.hpp"
#include "../include/PhysicsWorld.hpp"
#include <SFML/Graphics.hpp>
#include <cmath>


Player::Player(sf::Color color, b2WorldId worldId)
    : physicsWorldId(worldId), racket(nullptr), physicsBody(b2_nullBodyId)
{
    // 初始化渲染形状
    playerShape.setRadius(GameConfig::getPlayerRadius());
    // 设置原点为中心
    playerShape.setOrigin(GameConfig::getPlayerRadius(), GameConfig::getPlayerRadius());
    playerShape.setFillColor(color);
    legShape.setSize(sf::Vector2f(GameConfig::getPlayerRadius() * 1.5f, GameConfig::getPlayerRadius() * 0.5f));
    legShape.setOrigin(GameConfig::getPlayerRadius() * 0.75f, GameConfig::getPlayerRadius() * 0.25f);
    legShape.setFillColor(color);
    // 创建物理体
    createPhysicsBody();
    
    speed = GameConfig::PLAYER_SPEED / PhysicsWorld::PIXELS_PER_METER;

}

void Player::draw(sf::RenderWindow &window)
{
    updateFromPhysics();
    window.draw(playerShape);
    window.draw(legShape);
}

sf::Vector2f Player::getPosition() const
{
    return position;
}

void Player::setPosition(float x, float y)
{
    position = sf::Vector2f(x, y);
    playerShape.setPosition(position);

    // 更新腿部位置 - 腿部在玩家圆形底部
    float legY = y + GameConfig::getPlayerRadius();
    legShape.setPosition(x, legY);
}

void Player::move(Direction direction, float deltaTime)
{
    // 简化版本
    if (B2_IS_NULL(physicsBody))
        return;
    setPosition(position.x + direction.x() * speed * deltaTime, position.y+ direction.y() * speed * deltaTime);
}
bool Player::isOnGround() const
{
    if (B2_IS_NULL(physicsBody))
        return false;

    b2Vec2 velocity = b2Body_GetLinearVelocity(physicsBody);
    b2Vec2 pos = b2Body_GetPosition(physicsBody);

    // 检查是否接近地面且垂直速度很小
    float groundLevel = GameConfig::getGroundY() / GameConfig::PIXELS_PER_METER;
    float playerBottom = pos.y + GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER;

    return (playerBottom >= groundLevel - 0.1f) && (velocity.y >= -0.1f);
}

void Player::createPhysicsBody()
{
    if (B2_IS_NULL(physicsWorldId))
        return;

    b2BodyDef bodyDef = b2DefaultBodyDef();
    bodyDef.type = b2_dynamicBody;
    bodyDef.position = PhysicsWorld::sfToB2(position);

    physicsBody = b2CreateBody(physicsWorldId, &bodyDef);

    // 创建圆形碰撞体
    b2Circle circle;
    circle.center = {0.0f, 0.0f};
    circle.radius = GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER;

    b2ShapeDef shapeDef = b2DefaultShapeDef();
    shapeDef.density = 1.0f;

    b2ShapeId shapeId = b2CreateCircleShape(physicsBody, &shapeDef, &circle);

    // 设置完全非弹性碰撞，人物落地不会跳起
    b2Shape_SetRestitution(shapeId, 0.0f);

    // 创建球拍
    racket = new Racket(physicsWorldId, position, GameConfig::racketColor);
    // 将球杆用旋转关节连接到玩家上
    racket->createJoint(physicsWorldId, physicsBody, {GameConfig::getPlayerRadius() / GameConfig::PIXELS_PER_METER, 0.0f});
}

void Player::updateFromPhysics()
{
    if (B2_IS_NON_NULL(physicsBody))
    {
        b2Vec2 physicsPos = b2Body_GetPosition(physicsBody);
        sf::Vector2f newPos = PhysicsWorld::b2ToSf(physicsPos);
        setPosition(newPos.x, newPos.y);
    }
}

Player::~Player()
{
    if (racket)
    {
        delete racket;
    }
    if (B2_IS_NON_NULL(physicsBody) && B2_IS_NON_NULL(physicsWorldId))
    {
        b2DestroyBody(physicsBody);
    }
}