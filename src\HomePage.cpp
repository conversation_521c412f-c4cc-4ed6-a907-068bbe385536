#include "../include/HomePage.hpp"
#include "../include/GameConfig.hpp"
#include "../include/GameScene.hpp"

HomePage::HomePage(int width, int height, const char *title)
{
    GameConfig::initialize(width, height);
    window = new sf::RenderWindow(sf::VideoMode(width, height), title);
    window->setFramerateLimit(60);
    if (!font.loadFromFile("C:/Windows/Fonts/arial.ttf"))
    {
    }
    while (window->isOpen())
    {
        sf::Event event;
        if (event.type == sf::Event::Closed)
        {
            window->close();
        }
        setupTexts();

        // 监听鼠标点击事件，如果按下space，就开始游戏
        if (window->pollEvent(event))
        {
            if (event.type == sf::Event::KeyPressed)
            {
                if (event.key.code == sf::Keyboard::Space)
                {
                    GameScene gameScene;
                }
            }
        }
             
        window->clear();
        render();
        window->display();
    }
}

HomePage::~HomePage()
{
    delete window;
}

void HomePage::setupTexts()
{
    // 设置标题文本
    titleText.setFont(font);
    titleText.setString("Badminton Game");
    titleText.setCharacterSize(48);
    titleText.setFillColor(sf::Color::Black);
    titleText.setPosition(GameConfig::getTitleTextPos().x, GameConfig::getTitleTextPos().y);

    // 设置开始游戏文本
    startText.setFont(font);
    startText.setString("Press SPACE to Start Game");
    startText.setCharacterSize(24);
    startText.setFillColor(sf::Color::Blue);
    startText.setPosition(GameConfig::getStartTextPos().x, GameConfig::getStartTextPos().y);

    // 设置说明文本
    instructionText.setFont(font);
    instructionText.setString("Player 1: A/D-Move, W/S-Racket, Q-Jump\nPlayer 2: Arrow Keys-Move, Up/Down-Racket, RShift-Jump");
    instructionText.setCharacterSize(16);
    instructionText.setFillColor(sf::Color(128, 128, 128)); // 灰色
    instructionText.setPosition(GameConfig::getInstructionTextPos().x, GameConfig::getInstructionTextPos().y);
}

void HomePage::render()
{
    window->draw(titleText);
    window->draw(startText);
    window->draw(instructionText);
}
